<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use PDO;
use Exception;

class DatabaseServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $this->configureDatabaseConnection();
        $this->setupDatabaseEventListeners();
    }

    /**
     * Configure database connection with fallback support
     */
    private function configureDatabaseConnection(): void
    {
        try {
            // Priority 1: Try MySQL if available
            if (extension_loaded('pdo_mysql') && in_array('mysql', PDO::getAvailableDrivers())) {
                try {
                    $this->testMysqlConnection();
                    $this->optimizeMysqlConnection();
                    Log::info('MySQL connection established and optimized');
                    return;
                } catch (Exception $e) {
                    Log::warning('MySQL connection failed, trying fallback: ' . $e->getMessage());
                }
            }

            // Priority 2: Try SQLite fallback
            if (extension_loaded('pdo_sqlite') && in_array('sqlite', PDO::getAvailableDrivers())) {
                $this->configureFallbackDatabase();
                return;
            }

            // Priority 3: Emergency in-memory SQLite
            $this->configureEmergencyDatabase();

        } catch (Exception $e) {
            Log::error('All database configuration attempts failed: ' . $e->getMessage());
            throw new Exception('Database system unavailable: ' . $e->getMessage());
        }
    }

    /**
     * Test MySQL connection
     */
    private function testMysqlConnection(): void
    {
        try {
            $connection = DB::connection('mysql');
            $connection->getPdo();
            Log::info('MySQL connection established successfully');
        } catch (Exception $e) {
            Log::warning('MySQL connection failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Configure fallback database (SQLite)
     */
    private function configureFallbackDatabase(): void
    {
        try {
            // Ensure SQLite database file exists
            $sqliteDb = database_path('database.sqlite');
            if (!file_exists($sqliteDb)) {
                touch($sqliteDb);
                Log::info('Created SQLite database file: ' . $sqliteDb);
            }

            // Update default connection to SQLite
            Config::set('database.default', 'sqlite');
            Config::set('database.connections.sqlite.database', $sqliteDb);
            
            // Test SQLite connection
            $connection = DB::connection('sqlite');
            $connection->getPdo();
            
            Log::info('Fallback to SQLite database successful');
            
        } catch (Exception $e) {
            Log::error('SQLite fallback failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Setup database event listeners
     */
    private function setupDatabaseEventListeners(): void
    {
        // Log slow queries
        DB::listen(function ($query) {
            if ($query->time > 1000) { // Log queries taking more than 1 second
                Log::warning('Slow query detected', [
                    'sql' => $query->sql,
                    'bindings' => $query->bindings,
                    'time' => $query->time . 'ms'
                ]);
            }
        });

        // Handle connection errors gracefully
        DB::connection()->getEventDispatcher()->listen(
            'connection.failed',
            function ($event) {
                Log::error('Database connection failed', [
                    'connection' => $event->connection,
                    'exception' => $event->exception->getMessage()
                ]);
            }
        );
    }

    /**
     * Get database status information
     */
    public static function getDatabaseStatus(): array
    {
        $status = [
            'mysql_pdo_available' => extension_loaded('pdo_mysql'),
            'mysql_driver_available' => in_array('mysql', PDO::getAvailableDrivers()),
            'current_connection' => Config::get('database.default'),
            'connections_tested' => []
        ];

        // Test each connection
        $connections = ['mysql', 'sqlite'];
        foreach ($connections as $connection) {
            try {
                DB::connection($connection)->getPdo();
                $status['connections_tested'][$connection] = 'success';
            } catch (Exception $e) {
                $status['connections_tested'][$connection] = 'failed: ' . $e->getMessage();
            }
        }

        return $status;
    }

    /**
     * Force MySQL connection if available
     */
    public static function forceMysqlConnection(): bool
    {
        try {
            if (extension_loaded('pdo_mysql') && in_array('mysql', PDO::getAvailableDrivers())) {
                Config::set('database.default', 'mysql');
                DB::connection('mysql')->getPdo();
                Log::info('Forced MySQL connection successful');
                return true;
            }
        } catch (Exception $e) {
            Log::error('Force MySQL connection failed: ' . $e->getMessage());
        }
        
        return false;
    }

    /**
     * Migrate to SQLite if MySQL fails
     */
    public static function migrateToSqlite(): bool
    {
        try {
            $sqliteDb = database_path('database.sqlite');

            // Create SQLite database if it doesn't exist
            if (!file_exists($sqliteDb)) {
                $dir = dirname($sqliteDb);
                if (!is_dir($dir)) {
                    mkdir($dir, 0755, true);
                }
                touch($sqliteDb);
            }

            // Update configuration
            Config::set('database.default', 'sqlite');
            Config::set('database.connections.sqlite.database', $sqliteDb);
            Config::set('database.connections.sqlite.foreign_key_constraints', true);

            // Test connection
            $connection = DB::connection('sqlite');
            $pdo = $connection->getPdo();

            // Enable foreign keys for SQLite
            $pdo->exec('PRAGMA foreign_keys = ON');

            Log::info('Successfully migrated to SQLite database');
            return true;

        } catch (Exception $e) {
            Log::error('SQLite migration failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Optimize MySQL connection
     */
    private function optimizeMysqlConnection(): void
    {
        try {
            $connection = DB::connection('mysql');
            $pdo = $connection->getPdo();

            // Set MySQL session variables for better performance
            $pdo->exec("SET SESSION sql_mode = 'TRADITIONAL'");
            $pdo->exec("SET SESSION time_zone = '+00:00'");
            $pdo->exec("SET SESSION wait_timeout = 600");
            $pdo->exec("SET SESSION interactive_timeout = 600");

        } catch (Exception $e) {
            Log::warning('MySQL optimization failed: ' . $e->getMessage());
        }
    }

    /**
     * Configure emergency in-memory database
     */
    private function configureEmergencyDatabase(): void
    {
        try {
            Config::set('database.default', 'sqlite');
            Config::set('database.connections.sqlite.database', ':memory:');
            Config::set('database.connections.sqlite.foreign_key_constraints', false);

            $connection = DB::connection('sqlite');
            $connection->getPdo();

            Log::warning('Emergency in-memory database activated');

        } catch (Exception $e) {
            Log::error('Emergency database configuration failed: ' . $e->getMessage());
            throw $e;
        }
    }
}
