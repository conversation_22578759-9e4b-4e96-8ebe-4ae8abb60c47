<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use PDO;
use Exception;

class DatabaseServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $this->configureDatabaseConnection();
        $this->setupDatabaseEventListeners();
    }

    /**
     * Configure database connection with fallback support
     */
    private function configureDatabaseConnection(): void
    {
        try {
            // Check if MySQL PDO driver is available
            if (!extension_loaded('pdo_mysql') || !in_array('mysql', PDO::getAvailableDrivers())) {
                Log::warning('MySQL PDO driver not available, configuring fallback');
                $this->configureFallbackDatabase();
                return;
            }

            // Test MySQL connection
            $this->testMysqlConnection();
            
        } catch (Exception $e) {
            Log::error('Database configuration error: ' . $e->getMessage());
            $this->configureFallbackDatabase();
        }
    }

    /**
     * Test MySQL connection
     */
    private function testMysqlConnection(): void
    {
        try {
            $connection = DB::connection('mysql');
            $connection->getPdo();
            Log::info('MySQL connection established successfully');
        } catch (Exception $e) {
            Log::warning('MySQL connection failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Configure fallback database (SQLite)
     */
    private function configureFallbackDatabase(): void
    {
        try {
            // Ensure SQLite database file exists
            $sqliteDb = database_path('database.sqlite');
            if (!file_exists($sqliteDb)) {
                touch($sqliteDb);
                Log::info('Created SQLite database file: ' . $sqliteDb);
            }

            // Update default connection to SQLite
            Config::set('database.default', 'sqlite');
            Config::set('database.connections.sqlite.database', $sqliteDb);
            
            // Test SQLite connection
            $connection = DB::connection('sqlite');
            $connection->getPdo();
            
            Log::info('Fallback to SQLite database successful');
            
        } catch (Exception $e) {
            Log::error('SQLite fallback failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Setup database event listeners
     */
    private function setupDatabaseEventListeners(): void
    {
        // Log slow queries
        DB::listen(function ($query) {
            if ($query->time > 1000) { // Log queries taking more than 1 second
                Log::warning('Slow query detected', [
                    'sql' => $query->sql,
                    'bindings' => $query->bindings,
                    'time' => $query->time . 'ms'
                ]);
            }
        });

        // Handle connection errors gracefully
        DB::connection()->getEventDispatcher()->listen(
            'connection.failed',
            function ($event) {
                Log::error('Database connection failed', [
                    'connection' => $event->connection,
                    'exception' => $event->exception->getMessage()
                ]);
            }
        );
    }

    /**
     * Get database status information
     */
    public static function getDatabaseStatus(): array
    {
        $status = [
            'mysql_pdo_available' => extension_loaded('pdo_mysql'),
            'mysql_driver_available' => in_array('mysql', PDO::getAvailableDrivers()),
            'current_connection' => Config::get('database.default'),
            'connections_tested' => []
        ];

        // Test each connection
        $connections = ['mysql', 'sqlite'];
        foreach ($connections as $connection) {
            try {
                DB::connection($connection)->getPdo();
                $status['connections_tested'][$connection] = 'success';
            } catch (Exception $e) {
                $status['connections_tested'][$connection] = 'failed: ' . $e->getMessage();
            }
        }

        return $status;
    }

    /**
     * Force MySQL connection if available
     */
    public static function forceMysqlConnection(): bool
    {
        try {
            if (extension_loaded('pdo_mysql') && in_array('mysql', PDO::getAvailableDrivers())) {
                Config::set('database.default', 'mysql');
                DB::connection('mysql')->getPdo();
                Log::info('Forced MySQL connection successful');
                return true;
            }
        } catch (Exception $e) {
            Log::error('Force MySQL connection failed: ' . $e->getMessage());
        }
        
        return false;
    }

    /**
     * Migrate to SQLite if MySQL fails
     */
    public static function migrateToSqlite(): bool
    {
        try {
            $sqliteDb = database_path('database.sqlite');
            
            // Create SQLite database if it doesn't exist
            if (!file_exists($sqliteDb)) {
                touch($sqliteDb);
            }

            // Update configuration
            Config::set('database.default', 'sqlite');
            Config::set('database.connections.sqlite.database', $sqliteDb);
            
            // Test connection
            DB::connection('sqlite')->getPdo();
            
            Log::info('Successfully migrated to SQLite database');
            return true;
            
        } catch (Exception $e) {
            Log::error('SQLite migration failed: ' . $e->getMessage());
            return false;
        }
    }
}
